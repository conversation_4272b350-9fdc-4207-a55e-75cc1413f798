from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List, Any
from jobspy import scrape_jobs

# Import the FastAPI app objects from the other scrapers
from new_foundit import app as foundit_app
from new_glassdoor import app as glassdoor_app
from new_simplyhired import app as simplyhired_app
from new_ziprecruiter import app as ziprecruiter_app
from linkedin_job_detail_scraper import app as linkedin_detail_app
from naukri_job_detail_scraper import app as naukri_detail_app

app = FastAPI()

# Define allowed origins including ngrok URLs
allowed_origins = [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://localhost:3001",
    "https://localhost:3001",
    "https://445925a819f6.ngrok-free.app/",
    "*"  # Allow all origins for development
]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,  # Set to False when using "*" or multiple origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Add CORS middleware to all mounted apps
foundit_app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

glassdoor_app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

simplyhired_app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

ziprecruiter_app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

linkedin_detail_app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

naukri_detail_app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Mount the other apps under their own prefixes
app.mount("/foundit", foundit_app)
# For Glassdoor, use the parallel endpoints for all new requests
# /glassdoor/scrape_jobs_parallel (GET/POST) is the preferred endpoint
app.mount("/glassdoor", glassdoor_app)
app.mount("/simplyhired", simplyhired_app)
app.mount("/ziprecruiter", ziprecruiter_app)
app.mount("/linkedin", linkedin_detail_app)
app.mount("/naukri", naukri_detail_app)

class JobPortalRequest(BaseModel):
    search_term: Optional[str] = None
    google_search_term: Optional[str] = None
    location: Optional[str] = None
    results_wanted: Optional[int] = 5
    country_indeed: Optional[str] = "india"
    hours_old: Optional[int] = 72
    job_type: Optional[str] = None
    is_remote: Optional[bool] = None
    easy_apply: Optional[bool] = None
    description_format: Optional[str] = None
    offset: Optional[int] = 0
    verbose: Optional[int] = 2
    linkedin_fetch_description: Optional[bool] = None
    proxies: Optional[List[str]] = None


def run_scraper(params: dict) -> Any:
    try:
        jobs = scrape_jobs(**params)
        if jobs.empty:
            raise HTTPException(status_code=404, detail="No jobs found.")
        cleaned_jobs = jobs.replace([float("inf"), float("-inf")], None).fillna("")
        job_list = cleaned_jobs.to_dict(orient="records")

        # List of all possible fields for Indeed (from JobSpy docs)
        indeed_fields = [
            "title", "company", "company_url", "job_url", "location", "is_remote", "description", "job_type", "job_function", "interval", "min_amount", "max_amount", "currency", "salary_source", "date_posted", "emails", "company_industry", "company_country", "company_addresses", "company_employees_label", "company_revenue_label", "company_description", "company_logo", "job_level", "listing_type", "skills", "experience_range", "company_rating", "company_reviews_count", "vacancy_count", "work_from_home_type"
        ]

        # If this is an Indeed scrape, ensure all fields are present
        if params.get("site_name") == ["indeed"]:
            for job in job_list:
                for field in indeed_fields:
                    if field not in job:
                        job[field] = ""

        return {"message": f"Found {len(job_list)} jobs", "jobs": job_list}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



@app.post("/scrape-linkedin/")
async def scrape_linkedin(request: JobPortalRequest):
    # Extract only the fields needed for jobspy, excluding site_name
    params = {
        "search_term": request.search_term,
        "location": request.location,
        "results_wanted": request.results_wanted,
        "hours_old": request.hours_old,
        "job_type": request.job_type,
        "is_remote": request.is_remote,
        "easy_apply": request.easy_apply,
        "offset": request.offset,
        "verbose": request.verbose,
        "linkedin_fetch_description": request.linkedin_fetch_description,
        "proxies": request.proxies
    }
    # Remove None values
    params = {k: v for k, v in params.items() if v is not None}
    params["site_name"] = ["linkedin"]
    return run_scraper(params)

@app.post("/scrape-indeed/")
async def scrape_indeed(request: JobPortalRequest):
    params = request.dict(exclude_none=True)
    params["site_name"] = ["indeed"]
    return run_scraper(params)

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": "2025-07-02T00:19:00Z",
        "endpoints": [
            "/foundit/scrape_foundit",
            # Use only the parallel endpoint for Glassdoor
            "/glassdoor/scrape_jobs_parallel",
            "/simplyhired/scrape_simplyhired",
            "/ziprecruiter/scrape_ziprecruiter",
            "/scrape-linkedin/",
            "/scrape-indeed/",
            "/linkedin/scrape-linkedin/",
        ]
    } 